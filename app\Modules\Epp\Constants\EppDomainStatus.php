<?php

namespace App\Modules\Epp\Constants;

use App\Modules\Transfer\Constants\TransferRequest;

final class EppDomainStatus
{
    public const TRANSFER_PENDING = 'pending';

    public const TRANSFER_CLIENT_CONFLICT = 'clientConflict';

    public const TRANSFER_CLIENT_APPROVED = 'clientApproved';

    public const TRANSFER_CLIENT_CANCELLED = 'clientCancelled';

    public const TRANSFER_CLIENT_REJECTED = 'clientRejected';

    public const TRANSFER_SERVER_APPROVED = 'serverApproved';

    public const TRANSFER_SERVER_REJECTED = 'serverRejected';

    public const TRANSFER_STATUS = [
        TransferRequest::PENDING_REQUEST,
        TransferRequest::PENDING_APPROVAL,
        self::TRANSFER_PENDING,
        self::TRANSFER_CLIENT_APPROVED,
        self::TRANSFER_CLIENT_CANCELLED,
        self::TRANSFER_CLIENT_REJECTED,
        self::TRANSFER_SERVER_APPROVED,
        self::TRANSFER_CLIENT_CONFLICT,
        self::TRANSFER_SERVER_REJECTED,
        TransferRequest::SYSTEM_CANCELLED,
    ];

    public const SERVER_HOLD = 'serverHold';

    public const SERVER_RENEW_PROHIBITED = 'serverRenewProhibited';

    public const SERVER_TRANSFER_PROHIBITED = 'serverTransferProhibited';

    public const SERVER_UPDATE_PROHIBITED = 'serverUpdateProhibited';

    public const SERVER_DELETE_PROHIBITED = 'serverDeleteProhibited';

    public const SERVER_STATUS = [
        self::SERVER_HOLD,
        self::SERVER_RENEW_PROHIBITED,
        self::SERVER_TRANSFER_PROHIBITED,
        self::SERVER_UPDATE_PROHIBITED,
        self::SERVER_DELETE_PROHIBITED,
    ];

    public const INACTIVE = 'inactive';

    public const CLIENT_HOLD = 'clientHold';

    public const CLIENT_TRANSFER_PROHIBITED = 'clientTransferProhibited';

    public const CLIENT_UPDATE_PROHIBITED = 'clientUpdateProhibited';

    public const CLIENT_RENEW_PROHIBITED = 'clientRenewProhibited';

    public const CLIENT_DELETE_PROHIBITED = 'clientDeleteProhibited';

    public const CLIENT_STATUS = [
        self::CLIENT_HOLD,
        self::CLIENT_TRANSFER_PROHIBITED,
        self::CLIENT_UPDATE_PROHIBITED,
        self::CLIENT_RENEW_PROHIBITED,
        self::CLIENT_DELETE_PROHIBITED,
    ];

    public const CLIENT_LOCK_STATUS = [
        self::CLIENT_TRANSFER_PROHIBITED,
        self::CLIENT_DELETE_PROHIBITED,
    ];

    public const POST_AUTO_RENEWAL_GRACE_PERIOD_STATUS = [
        self::CLIENT_RENEW_PROHIBITED,
        self::CLIENT_UPDATE_PROHIBITED,
        self::CLIENT_TRANSFER_PROHIBITED,
    ];

    public const PENDING_CREATE = 'pendingCreate';

    public const PENDING_DELETE = 'pendingDelete';

    public const PENDING_RENEW = 'pendingRenew';

    public const PENDING_TRANSFER = 'pendingTransfer';

    public const PENDING_UPDATE = 'pendingUpdate';

    public const STATUS = [
        self::INACTIVE,
        self::PENDING_CREATE,
        self::PENDING_DELETE,
        self::PENDING_RENEW,
        self::PENDING_TRANSFER,
        self::PENDING_UPDATE,
    ];

    // from java class
    public const ELM_LOCALNAME = 'status';

    public const ELM_STATUS_OK = 'ok';

    public const ELM_DEFAULT_LANG = 'en';
}
