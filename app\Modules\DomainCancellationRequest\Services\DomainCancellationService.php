<?php

namespace App\Modules\DomainCancellationRequest\Services;

use App\Modules\AdminNotification\Services\AdminNotificationService;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\JobServices\JobDispatchService;
use App\Util\Helper\Domain\DomainParser;
use App\Events\DomainHistoryEvent;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DomainCancellationService
{
    public static function instance(): self
    {
        $DomainCancellationService = new self;

        return $DomainCancellationService;
    }
    public function addCancellationRequest($requestCancellation)
    {
        $registeredDomainId = $this->getRegisteredDomainId(
            $requestCancellation['domainId'],
            $requestCancellation['userID']
        );

        if (!$registeredDomainId) {
            throw new \Exception("No registered domain found for domain_id: {$requestCancellation['domainId']} and user_id: {$requestCancellation['userID']}");
        }

        $domainName = DB::table('domains')
            ->where('id', $requestCancellation['domainId'])
            ->value('name');

        self::updateDomainStatusDeletion($requestCancellation['domainId']);

        AdminNotificationService::instance()->sendDomainDeletionRequest($domainName);

        event(new DomainHistoryEvent([
            'domain_id' => $requestCancellation['domainId'],
            'type' => 'DOMAIN_CANCELLATION_REQUEST',
            'user_id' => $requestCancellation['userID'],
            'status' => 'success',
            'message' => 'Domain cancellation request initiated by ' . auth()->user()->email . '.',
            'payload' => $requestCancellation,
        ]));

        return DB::table('domain_cancellation_requests')->insert([
            'registered_domain_id' => $registeredDomainId,
            'reason' => $requestCancellation['reason'] === 'Others'
                ? $requestCancellation['custom_reason']
                : $requestCancellation['reason'],
            'requested_at' => Carbon::now(),
            'deleted_at' => null,
        ]);
    }

    public function getExpiredCancellationDomain($registeredDomainId)
    {
        return DB::table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->whereNull('deleted_at')
            ->first();
    }

    private function getRegisteredDomainId(int $domainId, int $userId): ?int
    {
        return DB::table('registered_domains')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->where('registered_domains.domain_id', $domainId)
            ->where('user_contacts.user_id', $userId)
            ->whereNull('registered_domains.deleted_at')
            ->value('registered_domains.id');
    }

    private function updateDomainStatusDeletion($domainID)
    {
        DB::table('domains')
            ->where('id', $domainID)
            ->update([
                'status' => DomainStatus::IN_PROCESS,
                'updated_at' => Carbon::now(),
            ]);
    }

    private function dispatchRemoveClientDeleteProhibitedJob(int $domainId, int $userId): void
    {
        $domain = $this->getDomainData($domainId);

        if (!$domain) {
            return;
        }

        $payload = $this->createJobPayload($domain, $userId);
        JobDispatchService::instance()->updateEppDispatch($payload);
    }

    private function getDomainData(int $domainId): ?object
    {
        $domain = DB::table('domains')
            ->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->select([
                'domains.id',
                'domains.name',
                'domains.registrant',
                'domains.client_status',
                'registered_domains.id as registered_domain_id',
            ])
            ->where('domains.id', $domainId)
            ->first();

        return $domain;
    }

    private function createJobPayload(object $domain, int $userId): array
    {
        $registry = DomainParser::getRegistryName($domain->name);
        $registeredDomain = [
            'id' => $domain->registered_domain_id,
            'name' => $domain->name,
        ];

        return [
            JobPayloadKeys::DOMAIN => $domain,
            JobPayloadKeys::REGISTERED_DOMAIN => json_decode(json_encode($registeredDomain), false),
            JobPayloadKeys::USER_ID => $userId,
            JobPayloadKeys::REGISTRY => $registry,
            JobPayloadKeys::EMAIL => auth()->user()->email ?? 'System',
            JobPayloadKeys::UPDATE_TYPE => DomainJobTypes::UPDATE_CANCELLATION,
        ];
    }
}
